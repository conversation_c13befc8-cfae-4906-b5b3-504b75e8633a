"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import {
  ChevronDown,
  ChevronUp,
  Trash2,
  ArrowLeft,
  ShoppingCart,
  CreditCard,
  Shield,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useCart } from "@/context/cart-context";
import { useAuth } from "@/context/auth-context";
import { motion, AnimatePresence } from "framer-motion";
import Navbar from "@/components/navbar";

export default function CheckoutPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { cart, removeFromCart, getCartTotal, clearCart } = useCart();
  const { user } = useAuth();
  const [expandedSegments, setExpandedSegments] = useState({});
  const [filteredCart, setFilteredCart] = useState([]);
  const [isEventSpecific, setIsEventSpecific] = useState(false);

  // Get eventId from URL parameters
  const eventId = searchParams.get("eventId");

  // Filter cart based on eventId parameter
  useEffect(() => {
    if (eventId && cart.length > 0) {
      const eventItems = cart.filter(
        (item) => item.eventId.toString() === eventId
      );
      setFilteredCart(eventItems);
      setIsEventSpecific(true);
    } else {
      setFilteredCart(cart);
      setIsEventSpecific(false);
    }
  }, [cart, eventId]);

  // Redirect if not logged in
  if (!user) {
    router.push("/login");
    return null;
  }

  // Redirect if cart is empty
  if (cart.length === 0) {
    router.push("/events");
    return null;
  }

  // Redirect if event-specific checkout but no items for that event
  if (isEventSpecific && filteredCart.length === 0) {
    router.push("/events");
    return null;
  }

  // Group cart items by event (segments) - use filtered cart
  const segments = filteredCart.reduce((groups, item, index) => {
    const itemEventId = item.eventId;
    if (!groups[itemEventId]) {
      groups[itemEventId] = {
        eventTitle: item.eventTitle,
        eventDate: item.eventDate,
        eventVenue: item.eventVenue || "TBA",
        items: [],
        subtotal: 0,
      };
    }
    groups[itemEventId].items.push({ ...item, originalIndex: index });
    groups[itemEventId].subtotal += item.price * item.quantity;
    return groups;
  }, {});

  const toggleSegment = (eventId) => {
    setExpandedSegments((prev) => ({
      ...prev,
      [eventId]: !prev[eventId],
    }));
  };

  const handleRemoveItem = (originalIndex) => {
    removeFromCart(originalIndex);
  };

  // Calculate totals based on filtered cart
  const getFilteredCartTotal = () => {
    return filteredCart.reduce(
      (total, item) => total + item.price * item.quantity,
      0
    );
  };

  const subtotal = getFilteredCartTotal();
  const organizerFees = subtotal * 0.05; // 5% organizer fee
  const serviceFees = subtotal * 0.1; // 10% service fee
  const totalAmount = subtotal + organizerFees + serviceFees;

  const handleProceedToPay = () => {
    // Here you would integrate with SSLCommerz
    // For now, we'll simulate the payment process
    alert("Redirecting to SSLCommerz payment gateway...");

    // Simulate successful payment
    setTimeout(() => {
      clearCart();
      router.push("/dashboard?payment=success");
    }, 2000);
  };

  return (
    <div className="min-h-screen bg-gray-950 text-white">
      <div className="mb-20">
        <Navbar />
      </div>

      {/* Header */}
      <div className="border-b border-zinc-800 bg-zinc-900/50 backdrop-blur-sm sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center space-x-4">
            <div className="flex items-center justify-between h-16">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.back()}
                className="text-zinc-400 hover:text-white"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <div className="flex items-center space-x-2">
                <ShoppingCart className="h-5 w-5 text-red-500" />
                <h1 className="text-xl font-bold">Checkout</h1>
              </div>
            </div>
            <div className="text-sm text-zinc-400">
              {filteredCart.reduce((count, item) => count + item.quantity, 0)}{" "}
              items
              {isEventSpecific && (
                <span className="ml-2 text-red-400">
                  (Event-specific checkout)
                </span>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Section - Tickets by Segments */}
          <div className="lg:col-span-2">
            <div className="bg-zinc-900 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-6 flex items-center">
                <CreditCard className="h-5 w-5 mr-2 text-red-500" />
                Your Tickets
              </h2>

              <div className="space-y-4">
                {Object.entries(segments).map(([eventId, segment]) => (
                  <div
                    key={eventId}
                    className="border border-zinc-700 rounded-lg overflow-hidden"
                  >
                    {/* Segment Header */}
                    <div
                      className="p-4 bg-zinc-800 cursor-pointer hover:bg-zinc-750 transition-colors"
                      onClick={() => toggleSegment(eventId)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h3 className="font-semibold text-lg">
                            {segment.eventTitle}
                          </h3>
                          <div className="flex items-center space-x-4 mt-1 text-sm text-zinc-400">
                            <span>
                              {new Date(segment.eventDate).toLocaleDateString()}
                            </span>
                            <span>•</span>
                            <span>{segment.eventVenue}</span>
                            <span>•</span>
                            <span>
                              {segment.items.length} ticket
                              {segment.items.length > 1 ? "s" : ""}
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center space-x-4">
                          <span className="font-semibold">
                            ${segment.subtotal.toFixed(2)}
                          </span>
                          {expandedSegments[eventId] ? (
                            <ChevronUp className="h-5 w-5 text-zinc-400" />
                          ) : (
                            <ChevronDown className="h-5 w-5 text-zinc-400" />
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Segment Content */}
                    <AnimatePresence>
                      {expandedSegments[eventId] && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: "auto", opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.2 }}
                          className="overflow-hidden"
                        >
                          <div className="p-4 space-y-3 bg-zinc-850">
                            {segment.items.map((item, index) => (
                              <div
                                key={index}
                                className="flex items-center justify-between p-3 bg-zinc-800 rounded-lg"
                              >
                                <div className="flex-1">
                                  <div className="font-medium">
                                    {item.ticketType}
                                  </div>
                                  <div className="text-sm text-zinc-400">
                                    ${item.price.toFixed(2)} × {item.quantity}
                                  </div>
                                </div>
                                <div className="flex items-center space-x-3">
                                  <span className="font-semibold">
                                    ${(item.price * item.quantity).toFixed(2)}
                                  </span>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() =>
                                      handleRemoveItem(item.originalIndex)
                                    }
                                    className="text-red-500 hover:text-red-400 hover:bg-red-500/10"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>
                            ))}
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Section - Payment Summary */}
          <div className="lg:col-span-1">
            <div className="bg-zinc-900 rounded-lg p-6 sticky top-24">
              <h2 className="text-xl font-semibold mb-6">Order Summary</h2>

              {/* Price Breakdown */}
              <div className="space-y-3 mb-6">
                <div className="flex justify-between text-zinc-300">
                  <span>Subtotal</span>
                  <span>${subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-zinc-300">
                  <span>Organizer Fees</span>
                  <span>${organizerFees.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-zinc-300">
                  <span>Service Fees</span>
                  <span>${serviceFees.toFixed(2)}</span>
                </div>
                <div className="border-t border-zinc-700 pt-3">
                  <div className="flex justify-between text-lg font-bold">
                    <span>Total Payable</span>
                    <span className="text-red-500">
                      ${totalAmount.toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Proceed to Pay Button */}
              <Button
                onClick={handleProceedToPay}
                className="w-full bg-red-600 hover:bg-red-700 text-white font-semibold py-3 mb-6"
                size="lg"
              >
                Proceed to Pay ${totalAmount.toFixed(2)}
              </Button>

              {/* Disclaimer */}
              <div className="space-y-4 text-xs text-zinc-400">
                <div className="flex items-start space-x-2">
                  <Shield className="h-4 w-4 mt-0.5 text-green-500 flex-shrink-0" />
                  <div>
                    <p className="font-medium text-zinc-300 mb-1">
                      Secure Payment
                    </p>
                    <p>
                      Your payment information is encrypted and secure. We use
                      industry-standard SSL encryption.
                    </p>
                  </div>
                </div>

                <div className="border-t border-zinc-800 pt-4">
                  <p className="font-medium text-zinc-300 mb-2">
                    Terms & Conditions
                  </p>
                  <ul className="space-y-1">
                    <li>• All ticket sales are final and non-refundable</li>
                    <li>
                      • Tickets are non-transferable unless specified by the
                      organizer
                    </li>
                    <li>
                      • Event details are subject to change by the organizer
                    </li>
                    <li>
                      • You must present valid ID matching the ticket holder's
                      name
                    </li>
                  </ul>
                </div>

                <div className="border-t border-zinc-800 pt-4">
                  <p className="font-medium text-zinc-300 mb-2">
                    Cancellation Policy
                  </p>
                  <p>
                    In case of event cancellation, full refunds will be
                    processed within 7-10 business days. Service fees are
                    non-refundable.
                  </p>
                </div>

                <div className="border-t border-zinc-800 pt-4">
                  <p className="text-center">
                    By proceeding with payment, you agree to our{" "}
                    <button className="text-red-500 hover:text-red-400 underline">
                      Terms of Service
                    </button>{" "}
                    and{" "}
                    <button className="text-red-500 hover:text-red-400 underline">
                      Privacy Policy
                    </button>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
